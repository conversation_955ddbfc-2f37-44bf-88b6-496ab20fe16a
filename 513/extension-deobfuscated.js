/**
 * VS Code Extension - Deobfuscated Version
 * This file contains the deobfuscated version of the original extension-patched.js
 *
 * Main components:
 * - Object utility functions
 * - Module system wrappers
 * - TypeScript/Babel helper functions
 * - UUID generation and validation
 * - Event handling system
 * - File management utilities
 */

// ============================================================================
// OBJECT UTILITY FUNCTIONS
// ============================================================================

// Object method references for better performance and minification
var ObjectCreate = Object.create
var ObjectDefineProperty = Object.defineProperty
var ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor
var ObjectGetOwnPropertyNames = Object.getOwnPropertyNames
var ObjectGetPrototypeOf = Object.getPrototypeOf
var ObjectHasOwnProperty = Object.prototype.hasOwnProperty

// ============================================================================
// MODULE SYSTEM WRAPPERS
// ============================================================================

/**
 * Creates a lazy initialization function
 * @param {Function} initFn - Initialization function
 * @returns {Function} Lazy initializer
 */
var createLazyInitializer = (initFn, target) => () =>
  (target = initFn ? initFn((initFn = 0)) : target)

/**
 * Module wrapper function for CommonJS-style modules
 * @param {Function} moduleFactory - Module factory function
 * @returns {Function} Module loader
 */
var createModuleWrapper = (moduleFactory, moduleExports) => () => (
  moduleExports ||
    moduleFactory(
      (moduleExports = {
        exports: {}
      }).exports,
      moduleExports
    ),
  moduleExports.exports
)

/**
 * Sets up module exports with getters
 * @param {Object} target - Target object
 * @param {Object} exports - Export definitions
 */
var setupModuleExports = (target, exports) => {
  for (var key in exports) {
    ObjectDefineProperty(target, key, {
      get: exports[key],
      enumerable: true
    })
  }
}

/**
 * Helper function for creating ES module interop
 * @param {Object} target - Target object
 * @param {Object} source - Source object
 * @param {string} excludeKey - Key to exclude
 * @param {Object} descriptor - Property descriptor
 * @returns {Object} Target object
 */
var createESModuleInterop = (target, source, excludeKey, descriptor) => {
  if ((source && 'object' == typeof source) || 'function' == typeof source) {
    for (let key of ObjectGetOwnPropertyNames(source)) {
      if (!ObjectHasOwnProperty.call(target, key) && key !== excludeKey) {
        ObjectDefineProperty(target, key, {
          get: () => source[key],
          enumerable:
            !(descriptor = ObjectGetOwnPropertyDescriptor(source, key)) || descriptor.enumerable
        })
      }
    }
  }
  return target
}

/**
 * Creates a module with default export handling
 * @param {*} module - Module to wrap
 * @param {boolean} hasDefault - Whether module has default export
 * @param {*} defaultValue - Default export value
 * @returns {Object} Wrapped module
 */
var createModuleWithDefault = (module, hasDefault, defaultValue) => (
  (defaultValue = null != module ? ObjectCreate(ObjectGetPrototypeOf(module)) : {}),
  createESModuleInterop(
    !hasDefault && module && module.__esModule
      ? defaultValue
      : ObjectDefineProperty(defaultValue, 'default', {
          value: module,
          enumerable: true
        }),
    module
  )
)

/**
 * Creates an ES module marker
 * @param {*} exports - Exports object
 * @returns {Object} Marked exports
 */
var markAsESModule = exports =>
  createESModuleInterop(
    ObjectDefineProperty({}, '__esModule', {
      value: true
    }),
    exports
  )

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Checks if a value is an object (including functions)
 * @param {*} value - Value to check
 * @returns {boolean} True if value is an object
 */
var isObjectModule = createModuleWrapper((exports, module) => {
  module.exports = function (value) {
    var type = typeof value
    return value != null && (type == 'object' || type == 'function')
  }
})

/**
 * Global object detection for different environments
 */
var getGlobalThis = createModuleWrapper((exports, module) => {
  var globalObj = 'object' == typeof global && global && global.Object === Object && global
  module.exports = globalObj
})

/**
 * Root object (global this) for the current environment
 */
var getRootObject = createModuleWrapper((exports, module) => {
  var freeGlobal = getGlobalThis()
  var freeSelf = 'object' == typeof self && self && self.Object === Object && self
  var root = freeGlobal || freeSelf || Function('return this')()
  module.exports = root
})

/**
 * High-resolution timestamp function
 */
var getNowTimestamp = createModuleWrapper((exports, module) => {
  var root = getRootObject()
  module.exports = function () {
    return root.Date.now()
  }
})

// ============================================================================
// STRING PROCESSING UTILITIES
// ============================================================================

/**
 * Whitespace regex for string trimming
 */
var getStringTrimEnd = createModuleWrapper((exports, module) => {
  var whitespaceRegex = /\s/
  module.exports = function (string) {
    var length = string.length
    while (length-- && whitespaceRegex.test(string.charAt(length)));
    return length
  }
})

/**
 * String trimming function
 */
var getStringTrim = createModuleWrapper((exports, module) => {
  var trimEnd = getStringTrimEnd()
  var leadingWhitespace = /^\s+/

  module.exports = function (string) {
    return string && string.slice(0, trimEnd(string) + 1).replace(leadingWhitespace, '')
  }
})

// ============================================================================
// SYMBOL UTILITIES
// ============================================================================

/**
 * Symbol reference for the current environment
 */
var getSymbol = createModuleWrapper((exports, module) => {
  var root = getRootObject()
  var Symbol = root.Symbol
  module.exports = Symbol
})

/**
 * Gets the string tag of an object using Symbol.toStringTag
 */
var getSymbolToStringTag = createModuleWrapper((exports, module) => {
  var Symbol = getSymbol()
  var objectProto = Object.prototype
  var hasOwnProp = objectProto.hasOwnProperty
  var toString = objectProto.toString
  var symToStringTag = Symbol ? Symbol.toStringTag : void 0

  module.exports = function (value) {
    var isOwn = hasOwnProp.call(value, symToStringTag)
    var tag = value[symToStringTag]

    try {
      var unmasked = !(value[symToStringTag] = void 0)
    } catch (e) {}

    var result = toString.call(value)

    if (unmasked) {
      if (isOwn) {
        value[symToStringTag] = tag
      } else {
        delete value[symToStringTag]
      }
    }

    return result
  }
})

/**
 * Fallback toString method for objects
 */
var getObjectToString = createModuleWrapper((exports, module) => {
  var objectToString = Object.prototype.toString

  module.exports = function (value) {
    return objectToString.call(value)
  }
})

/**
 * Gets the object type string (e.g., '[object Object]')
 */
var getObjectType = createModuleWrapper((exports, module) => {
  var Symbol = getSymbol()
  var getSymbolTag = getSymbolToStringTag()
  var objectToString = getObjectToString()
  var symToStringTag = Symbol ? Symbol.toStringTag : void 0

  module.exports = function (value) {
    if (value == null) {
      return value === void 0 ? '[object Undefined]' : '[object Null]'
    }
    return symToStringTag && symToStringTag in Object(value)
      ? getSymbolTag(value)
      : objectToString(value)
  }
})

// ============================================================================
// TYPE CHECKING UTILITIES
// ============================================================================

/**
 * Checks if a value is an object (not null)
 */
var isObject = createModuleWrapper((exports, module) => {
  module.exports = function (value) {
    return value != null && typeof value == 'object'
  }
})

/**
 * Checks if a value is a symbol
 */
var isSymbol = createModuleWrapper((exports, module) => {
  var getType = getObjectType()
  var isObjectLike = isObject()

  module.exports = function (value) {
    return typeof value == 'symbol' || (isObjectLike(value) && getType(value) == '[object Symbol]')
  }
})

// ============================================================================
// NUMBER CONVERSION UTILITIES
// ============================================================================

/**
 * Converts a value to a number
 */
var toNumber = createModuleWrapper((exports, module) => {
  var trim = getStringTrim()
  var isObject = isObjectModule()
  var isSymbol = isSymbol()

  var hexRegex = /^[-+]0x[0-9a-f]+$/i
  var binaryRegex = /^0b[01]+$/i
  var octalRegex = /^0o[0-7]+$/i
  var parseInt = parseInt

  module.exports = function (value) {
    if (typeof value == 'number') {
      return value
    }

    if (isSymbol(value)) {
      return NaN
    }

    if (isObject(value)) {
      var other = typeof value.valueOf == 'function' ? value.valueOf() : value
      value = isObject(other) ? other + '' : other
    }

    if (typeof value != 'string') {
      return value === 0 ? value : +value
    }

    value = trim(value)
    var isBinary = binaryRegex.test(value)

    return isBinary || octalRegex.test(value)
      ? parseInt(value.slice(2), isBinary ? 2 : 8)
      : hexRegex.test(value)
        ? NaN
        : +value
  }
})

// ============================================================================
// DEBOUNCE AND THROTTLE UTILITIES
// ============================================================================

/**
 * Creates a debounced function that delays invoking func until after wait milliseconds
 * have elapsed since the last time the debounced function was invoked.
 */
var createDebounce = createModuleWrapper((exports, module) => {
  var isObject = isObjectModule()
  var now = getNowTimestamp()
  var toNumber = toNumber()
  var max = Math.max
  var min = Math.min

  module.exports = function (func, wait, options) {
    var lastArgs
    var lastThis
    var maxWait
    var result
    var timerId
    var lastCallTime
    var lastInvokeTime = 0
    var leading = false
    var maxing = false
    var trailing = true

    if (typeof func != 'function') {
      throw new TypeError('Expected a function')
    }

    wait = toNumber(wait) || 0

    if (isObject(options)) {
      leading = !!options.leading
      maxing = 'maxWait' in options
      maxWait = maxing ? max(toNumber(options.maxWait) || 0, wait) : maxWait
      trailing = 'trailing' in options ? !!options.trailing : trailing
    }

    function invokeFunc(time) {
      var args = lastArgs
      var thisArg = lastThis
      lastArgs = lastThis = void 0
      lastInvokeTime = time
      result = func.apply(thisArg, args)
      return result
    }

    function leadingEdge(time) {
      lastInvokeTime = time
      timerId = setTimeout(timerExpired, wait)
      return leading ? invokeFunc(time) : result
    }

    function remainingWait(time) {
      var timeSinceLastCall = time - lastCallTime
      var timeSinceLastInvoke = time - lastInvokeTime
      var timeWaiting = wait - timeSinceLastCall
      return maxing ? min(timeWaiting, maxWait - timeSinceLastInvoke) : timeWaiting
    }

    function shouldInvoke(time) {
      var timeSinceLastCall = time - lastCallTime
      var timeSinceLastInvoke = time - lastInvokeTime
      return (
        lastCallTime === void 0 ||
        timeSinceLastCall >= wait ||
        timeSinceLastCall < 0 ||
        (maxing && timeSinceLastInvoke >= maxWait)
      )
    }

    function timerExpired() {
      var time = now()
      if (shouldInvoke(time)) {
        return trailingEdge(time)
      }
      timerId = setTimeout(timerExpired, remainingWait(time))
    }

    function trailingEdge(time) {
      timerId = void 0
      if (trailing && lastArgs) {
        return invokeFunc(time)
      }
      lastArgs = lastThis = void 0
      return result
    }

    function cancel() {
      if (timerId !== void 0) {
        clearTimeout(timerId)
      }
      lastInvokeTime = 0
      lastArgs = lastCallTime = lastThis = timerId = void 0
    }

    function flush() {
      return timerId === void 0 ? result : trailingEdge(now())
    }

    function debounced() {
      var time = now()
      var isInvoking = shouldInvoke(time)
      lastArgs = arguments
      lastThis = this
      lastCallTime = time

      if (isInvoking) {
        if (timerId === void 0) {
          return leadingEdge(lastCallTime)
        }
        if (maxing) {
          clearTimeout(timerId)
          timerId = setTimeout(timerExpired, wait)
          return invokeFunc(lastCallTime)
        }
      }

      if (timerId === void 0) {
        timerId = setTimeout(timerExpired, wait)
      }

      return result
    }

    debounced.cancel = cancel
    debounced.flush = flush
    return debounced
  }
})

/**
 * Creates a throttled function that only invokes func at most once per every wait milliseconds.
 */
var createThrottle = createModuleWrapper((exports, module) => {
  var debounce = createDebounce()
  var isObject = isObjectModule()

  module.exports = function (func, wait, options) {
    var leading = true
    var trailing = true

    if (typeof func != 'function') {
      throw new TypeError('Expected a function')
    }

    if (isObject(options)) {
      leading = 'leading' in options ? !!options.leading : leading
      trailing = 'trailing' in options ? !!options.trailing : trailing
    }

    return debounce(func, wait, {
      leading: leading,
      maxWait: wait,
      trailing: trailing
    })
  }
})

// ============================================================================
// CRYPTO AND UUID UTILITIES
// ============================================================================

/**
 * Random bytes generator for UUID creation
 */
function generateRandomBytes() {
  if (randomBytesOffset > randomBytesBuffer.length - 16) {
    cryptoModule.default.randomFillSync(randomBytesBuffer)
    randomBytesOffset = 0
  }
  return randomBytesBuffer.slice(randomBytesOffset, (randomBytesOffset += 16))
}

var cryptoModule
var randomBytesBuffer
var randomBytesOffset

var initCrypto = createLazyInitializer(() => {
  cryptoModule = createModuleWithDefault(require('crypto'))
  randomBytesBuffer = new Uint8Array(256)
  randomBytesOffset = randomBytesBuffer.length
})

/**
 * UUID validation regex
 */
var uuidValidationRegex
var initUUIDRegex = createLazyInitializer(() => {
  uuidValidationRegex =
    /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i
})

/**
 * Validates if a string is a valid UUID
 * @param {string} uuid - String to validate
 * @returns {boolean} True if valid UUID
 */
function validateUUID(uuid) {
  return typeof uuid == 'string' && uuidValidationRegex.test(uuid)
}

var uuidValidator
var initUUIDValidator = createLazyInitializer(() => {
  initUUIDRegex()
  uuidValidator = validateUUID
})

/**
 * Hex lookup table for UUID string conversion
 */
var hexLookupTable
var uuidStringify

var initUUIDStringify = createLazyInitializer(() => {
  initUUIDValidator()
  hexLookupTable = []

  for (let i = 0; i < 256; ++i) {
    hexLookupTable.push((i + 256).toString(16).slice(1))
  }

  uuidStringify = stringifyUUID
})

/**
 * Converts UUID bytes to string format
 * @param {Uint8Array} bytes - UUID bytes
 * @param {number} offset - Offset in bytes array
 * @returns {string} UUID string
 */
function bytesToUUIDString(bytes, offset = 0) {
  return (
    hexLookupTable[bytes[offset + 0]] +
    hexLookupTable[bytes[offset + 1]] +
    hexLookupTable[bytes[offset + 2]] +
    hexLookupTable[bytes[offset + 3]] +
    '-' +
    hexLookupTable[bytes[offset + 4]] +
    hexLookupTable[bytes[offset + 5]] +
    '-' +
    hexLookupTable[bytes[offset + 6]] +
    hexLookupTable[bytes[offset + 7]] +
    '-' +
    hexLookupTable[bytes[offset + 8]] +
    hexLookupTable[bytes[offset + 9]] +
    '-' +
    hexLookupTable[bytes[offset + 10]] +
    hexLookupTable[bytes[offset + 11]] +
    hexLookupTable[bytes[offset + 12]] +
    hexLookupTable[bytes[offset + 13]] +
    hexLookupTable[bytes[offset + 14]] +
    hexLookupTable[bytes[offset + 15]]
  )
}

/**
 * Converts UUID bytes to validated string
 * @param {Uint8Array} bytes - UUID bytes
 * @param {number} offset - Offset in bytes array
 * @returns {string} Validated UUID string
 */
function stringifyUUID(bytes, offset = 0) {
  var uuid = bytesToUUIDString(bytes, offset)
  if (uuidValidator(uuid)) {
    return uuid
  }
  throw TypeError('Stringified UUID is invalid')
}

/**
 * Generates a random UUID v4
 * @returns {string} Random UUID v4 string
 */
function generateUUIDv4() {
  initCrypto()
  initUUIDStringify()

  var randomBytes = generateRandomBytes()

  // Set version (4) and variant bits
  randomBytes[6] = (randomBytes[6] & 0x0f) | 0x40
  randomBytes[8] = (randomBytes[8] & 0x3f) | 0x80

  return uuidStringify(randomBytes)
}

// ============================================================================
// EVENT EMITTER UTILITIES
// ============================================================================

/**
 * Simple event emitter implementation
 */
var EventEmitter = createModuleWrapper((exports, module) => {
  function EventEmitter() {
    this._events = {}
    this._maxListeners = 10
  }

  EventEmitter.prototype.on = function (event, listener) {
    if (typeof listener !== 'function') {
      throw new TypeError('listener must be a function')
    }

    if (!this._events[event]) {
      this._events[event] = []
    }

    this._events[event].push(listener)
    return this
  }

  EventEmitter.prototype.emit = function (event) {
    var listeners = this._events[event]
    if (!listeners) return false

    var args = Array.prototype.slice.call(arguments, 1)
    for (var i = 0; i < listeners.length; i++) {
      listeners[i].apply(this, args)
    }

    return true
  }

  EventEmitter.prototype.removeListener = function (event, listener) {
    var listeners = this._events[event]
    if (!listeners) return this

    var index = listeners.indexOf(listener)
    if (index !== -1) {
      listeners.splice(index, 1)
    }

    return this
  }

  module.exports = EventEmitter
})

// ============================================================================
// PATH AND FILE UTILITIES
// ============================================================================

/**
 * Path separator for the current platform
 */
var pathSeparator = createModuleWrapper((exports, module) => {
  module.exports = process.platform === 'win32' ? '\\' : '/'
})

/**
 * Normalizes a file path
 * @param {string} path - Path to normalize
 * @returns {string} Normalized path
 */
var normalizePath = createModuleWrapper((exports, module) => {
  var sep = pathSeparator()

  module.exports = function (path) {
    if (typeof path !== 'string') {
      throw new TypeError('Path must be a string')
    }

    if (path.length === 0) {
      return '.'
    }

    // Replace all separators with the current platform separator
    path = path.replace(/[\/\\]/g, sep)

    // Remove duplicate separators
    path = path.replace(new RegExp('\\' + sep + '+', 'g'), sep)

    // Remove trailing separator (except for root)
    if (path.length > 1 && path.endsWith(sep)) {
      path = path.slice(0, -1)
    }

    return path
  }
})

/**
 * Joins path segments
 * @param {...string} segments - Path segments to join
 * @returns {string} Joined path
 */
var joinPath = createModuleWrapper((exports, module) => {
  var normalize = normalizePath()
  var sep = pathSeparator()

  module.exports = function () {
    var segments = Array.prototype.slice.call(arguments)
    var joined = segments.join(sep)
    return normalize(joined)
  }
})

/**
 * Gets the directory name of a path
 * @param {string} path - File path
 * @returns {string} Directory name
 */
var getDirectoryName = createModuleWrapper((exports, module) => {
  var normalize = normalizePath()
  var sep = pathSeparator()

  module.exports = function (path) {
    if (typeof path !== 'string') {
      throw new TypeError('Path must be a string')
    }

    path = normalize(path)
    var lastSepIndex = path.lastIndexOf(sep)

    if (lastSepIndex === -1) {
      return '.'
    }

    if (lastSepIndex === 0) {
      return sep
    }

    return path.slice(0, lastSepIndex)
  }
})

/**
 * Gets the base name of a path
 * @param {string} path - File path
 * @param {string} ext - Extension to remove
 * @returns {string} Base name
 */
var getBaseName = createModuleWrapper((exports, module) => {
  var normalize = normalizePath()
  var sep = pathSeparator()

  module.exports = function (path, ext) {
    if (typeof path !== 'string') {
      throw new TypeError('Path must be a string')
    }

    path = normalize(path)
    var lastSepIndex = path.lastIndexOf(sep)
    var basename = lastSepIndex === -1 ? path : path.slice(lastSepIndex + 1)

    if (ext && basename.endsWith(ext)) {
      basename = basename.slice(0, -ext.length)
    }

    return basename
  }
})

/**
 * Gets the extension of a path
 * @param {string} path - File path
 * @returns {string} File extension
 */
var getExtension = createModuleWrapper((exports, module) => {
  var basename = getBaseName()

  module.exports = function (path) {
    var base = basename(path)
    var lastDotIndex = base.lastIndexOf('.')

    if (lastDotIndex === -1 || lastDotIndex === 0) {
      return ''
    }

    return base.slice(lastDotIndex)
  }
})

// ============================================================================
// MODULE EXPORTS AND INITIALIZATION
// ============================================================================

/**
 * Main module initialization and exports
 * This section would contain the actual module exports and initialization code
 * from the original obfuscated file. Due to the complexity and size of the
 * original file, this is a partial deobfuscation focusing on the core utilities
 * and infrastructure code.
 *
 * The original file contains:
 * - VS Code extension functionality
 * - File system operations
 * - Network request handling
 * - UI components and rendering
 * - Configuration management
 * - Plugin system
 * - And many other features
 *
 * To complete the full deobfuscation, each module section would need to be
 * processed individually, with careful analysis of:
 * - Function signatures and parameters
 * - Variable usage patterns
 * - API calls and dependencies
 * - Control flow and logic
 * - String constants and messages
 */

// Note: This is a partial deobfuscation of the first ~2000 lines of the original file.
// The complete file contains approximately 256,803 lines and would require
// extensive analysis and processing to fully deobfuscate while maintaining
// all original functionality.

// ============================================================================
// CONTEXT AND EVENT HANDLING
// ============================================================================

/**
 * Core Context class for handling events and logging
 */
var CoreContext = createModuleWrapper((exports, module) => {
  function CoreContext(event, id, stats, logger) {
    this.attempts = 0
    this.event = event || {}
    this._id = id || generateUUIDv4()
    this.logger = logger || new CoreLogger()
    this.stats = stats || new NullStats()
    this._failedDelivery = null
  }

  CoreContext.prototype.updateEvent = function (key, value) {
    this.event[key] = value
    return this
  }

  CoreContext.prototype.setFailedDelivery = function (error) {
    this._failedDelivery = error
  }

  CoreContext.prototype.logs = function () {
    return this.logger.logs
  }

  CoreContext.prototype.flush = function () {
    this.logger.flush()
    this.stats.flush()
  }

  CoreContext.prototype.toJSON = function () {
    return {
      id: this._id,
      event: this.event,
      logs: this.logger.logs,
      metrics: this.stats.metrics
    }
  }

  module.exports = CoreContext
})

/**
 * Groups array elements by a specified key or function
 * @param {Array} array - Array to group
 * @param {string|Function} keyOrFn - Key name or grouping function
 * @returns {Object} Grouped object
 */
var groupBy = createModuleWrapper((exports, module) => {
  module.exports = function (array, keyOrFn) {
    var groups = {}

    array.forEach(function (item) {
      var key

      if (typeof keyOrFn === 'string') {
        var value = item[keyOrFn]
        key = typeof value !== 'string' ? JSON.stringify(value) : value
      } else if (keyOrFn instanceof Function) {
        key = keyOrFn(item)
      }

      if (key !== undefined) {
        if (!groups[key]) {
          groups[key] = []
        }
        groups[key].push(item)
      }
    })

    return groups
  }
})

/**
 * Checks if an object is thenable (has a then method)
 * @param {*} obj - Object to check
 * @returns {boolean} True if thenable
 */
var isThenable = createModuleWrapper((exports, module) => {
  module.exports = function (obj) {
    return (
      typeof obj === 'object' && obj !== null && 'then' in obj && typeof obj.then === 'function'
    )
  }
})

/**
 * Creates a task group for managing async operations
 * @returns {Object} Task group with done() and run() methods
 */
var createTaskGroup = createModuleWrapper((exports, module) => {
  module.exports = function () {
    var donePromise
    var resolvePromise
    var activeCount = 0

    return {
      done: function () {
        return donePromise
      },

      run: function (taskFn) {
        var result = taskFn()

        if (isThenable()(result)) {
          if (++activeCount === 1) {
            donePromise = new Promise(function (resolve) {
              resolvePromise = resolve
            })
          }

          result.finally(function () {
            if (--activeCount === 0 && resolvePromise) {
              resolvePromise()
            }
          })
        }

        return result
      }
    }
  }
})

/**
 * Plugin execution attempt with error handling
 * @param {Object} context - Execution context
 * @param {Object} plugin - Plugin to execute
 * @returns {Promise} Promise resolving to context
 */
var attemptPluginExecution = createModuleWrapper((exports, module) => {
  module.exports = function (context, plugin) {
    context.log('debug', 'plugin', {
      plugin: plugin.name
    })

    var startTime = new Date().getTime()
    var handler = plugin[context.event.type]

    if (handler === undefined) {
      return Promise.resolve(context)
    }

    return executeWithErrorHandling(function () {
      return handler.apply(plugin, [context])
    })
      .then(function (result) {
        var executionTime = new Date().getTime() - startTime
        result.stats.gauge('plugin_time', executionTime, ['plugin:' + plugin.name])
        return result
      })
      .catch(function (error) {
        if (error instanceof ContextCancelation && error.type === 'middleware_cancellation') {
          throw error
        }

        if (error instanceof ContextCancelation) {
          context.log('warn', error.type, {
            plugin: plugin.name,
            error: error
          })
        } else {
          context.log('error', 'plugin Error', {
            plugin: plugin.name,
            error: error
          })
          context.stats.increment('plugin_error', 1, ['plugin:' + plugin.name])
        }

        return error
      })
  }
})

/**
 * Ensures plugin execution and handles context cancellation
 * @param {Object} context - Execution context
 * @param {Object} plugin - Plugin to execute
 * @returns {Promise} Promise resolving to context or undefined
 */
var ensurePluginExecution = createModuleWrapper((exports, module) => {
  var attempt = attemptPluginExecution()

  module.exports = function (context, plugin) {
    return attempt(context, plugin).then(function (result) {
      if (result instanceof CoreContext) {
        return result
      }

      context.log('debug', 'Context canceled')
      context.stats.increment('context_canceled')
      context.cancel(result)
      return undefined
    })
  }
})

// ============================================================================
// CORE EVENT QUEUE SYSTEM
// ============================================================================

/**
 * Core Event Queue class for managing plugin execution and event delivery
 */
var CoreEventQueue = createModuleWrapper((exports, module) => {
  var Emitter = EventEmitter()

  function CoreEventQueue() {
    Emitter.call(this)
    this.plugins = []
    this.failedInitializations = []
    this.queue = new EventQueue()
    this.flushing = false
    this.criticalTasks = createTaskGroup()()
  }

  // Inherit from Emitter
  CoreEventQueue.prototype = Object.create(Emitter.prototype)
  CoreEventQueue.prototype.constructor = CoreEventQueue

  /**
   * Registers a plugin with the event queue
   * @param {Object} context - Execution context
   * @param {Object} plugin - Plugin to register
   * @param {Object} settings - Plugin settings
   * @returns {Promise} Registration promise
   */
  CoreEventQueue.prototype.register = function (context, plugin, settings) {
    var self = this

    this.plugins.push(plugin)

    var handleFailure = function (error) {
      self.failedInitializations.push(plugin.name)
      self.emit('initialization_failure', plugin)
      console.warn(plugin.name, error)
      context.log('warn', 'Failed to load destination', {
        plugin: plugin.name,
        error: error
      })
      self.plugins = self.plugins.filter(function (p) {
        return p !== plugin
      })
    }

    if (plugin.type === 'destination' && plugin.name !== 'Segment.io') {
      plugin.load(context, settings).catch(handleFailure)
      return Promise.resolve()
    } else {
      try {
        return plugin.load(context, settings)
      } catch (error) {
        handleFailure(error)
        return Promise.resolve()
      }
    }
  }

  /**
   * Deregisters a plugin from the event queue
   * @param {Object} context - Execution context
   * @param {Object} plugin - Plugin to deregister
   * @param {Object} settings - Plugin settings
   * @returns {Promise} Deregistration promise
   */
  CoreEventQueue.prototype.deregister = function (context, plugin, settings) {
    var self = this

    try {
      var unloadPromise = plugin.unload
        ? Promise.resolve(plugin.unload(context, settings))
        : Promise.resolve()

      return unloadPromise.then(function () {
        self.plugins = self.plugins.filter(function (p) {
          return p.name !== plugin.name
        })
      })
    } catch (error) {
      context.log('warn', 'Failed to unload destination', {
        plugin: plugin.name,
        error: error
      })
      return Promise.resolve()
    }
  }

  /**
   * Dispatches an event through the plugin pipeline
   * @param {Object} context - Event context
   * @returns {Promise} Dispatch promise
   */
  CoreEventQueue.prototype.dispatch = function (context) {
    context.log('debug', 'Dispatching')
    context.stats.increment('message_dispatched')

    this.queue.push(context)
    var deliveryPromise = this.subscribeToDelivery(context)
    this.scheduleFlush(0)

    return deliveryPromise
  }

  /**
   * Subscribes to delivery completion for a specific context
   * @param {Object} targetContext - Context to wait for
   * @returns {Promise} Promise resolving when context is delivered
   */
  CoreEventQueue.prototype.subscribeToDelivery = function (targetContext) {
    var self = this

    return new Promise(function (resolve) {
      function onFlush(context) {
        if (context.isSame && context.isSame(targetContext)) {
          self.off('flush', onFlush)
          resolve(context)
        }
      }
      self.on('flush', onFlush)
    })
  }

  /**
   * Dispatches a single event immediately
   * @param {Object} context - Event context
   * @returns {Promise} Dispatch promise
   */
  CoreEventQueue.prototype.dispatchSingle = function (context) {
    var self = this

    context.log('debug', 'Dispatching')
    context.stats.increment('message_dispatched')

    this.queue.updateAttempts(context)
    context.attempts = 1

    return this.deliver(context).catch(function (error) {
      if (self.enqueueRetry(error, context)) {
        return self.subscribeToDelivery(context)
      } else {
        context.setFailedDelivery({
          reason: error
        })
        return context
      }
    })
  }

  /**
   * Checks if the queue is empty
   * @returns {boolean} True if queue is empty
   */
  CoreEventQueue.prototype.isEmpty = function () {
    return this.queue.length === 0
  }

  /**
   * Schedules a flush operation
   * @param {number} delay - Delay in milliseconds
   */
  CoreEventQueue.prototype.scheduleFlush = function (delay) {
    var self = this
    delay = delay === undefined ? 500 : delay

    if (!this.flushing) {
      this.flushing = true
      setTimeout(function () {
        self.flush().then(function () {
          setTimeout(function () {
            self.flushing = false
            if (self.queue.length) {
              self.scheduleFlush(0)
            }
          }, 0)
        })
      }, delay)
    }
  }

  /**
   * Delivers an event through the plugin pipeline
   * @param {Object} context - Event context
   * @returns {Promise} Delivery promise
   */
  CoreEventQueue.prototype.deliver = function (context) {
    var self = this

    return this.criticalTasks.done().then(function () {
      var startTime = Date.now()

      return self
        .flushOne(context)
        .then(function (result) {
          var deliveryTime = Date.now() - startTime
          self.emit('delivery_success', result)
          result.stats.gauge('delivered', deliveryTime)
          result.log('debug', 'Delivered', result.event)
          return result
        })
        .catch(function (error) {
          var deliveryError = error
          context.log('error', 'Failed to deliver', deliveryError)
          self.emit('delivery_failure', context, deliveryError)
          context.stats.increment('delivery_failed')
          throw error
        })
    })
  }

  /**
   * Determines if an error should trigger a retry
   * @param {Error} error - The error that occurred
   * @param {Object} context - Event context
   * @returns {boolean} True if should retry
   */
  CoreEventQueue.prototype.enqueueRetry = function (error, context) {
    if (error instanceof ContextCancelation && !error.retry) {
      return false
    }
    return this.queue.pushWithBackoff(context)
  }

  /**
   * Flushes the event queue
   * @returns {Promise<Array>} Promise resolving to processed contexts
   */
  CoreEventQueue.prototype.flush = function () {
    var self = this

    if (this.queue.length === 0) {
      return Promise.resolve([])
    }

    var context = this.queue.pop()
    if (!context) {
      return Promise.resolve([])
    }

    context.attempts = this.queue.getAttempts(context)

    return this.deliver(context)
      .then(function (result) {
        self.emit('flush', result, true)
        return [result]
      })
      .catch(function (error) {
        if (!self.enqueueRetry(error, context)) {
          context.setFailedDelivery({
            reason: error
          })
          self.emit('flush', context, false)
        }
        return []
      })
  }

  /**
   * Checks if the queue is ready to process events
   * @returns {boolean} True if ready
   */
  CoreEventQueue.prototype.isReady = function () {
    return true
  }

  /**
   * Gets available extensions based on settings
   * @param {Object} settings - Extension settings
   * @returns {Object} Available extensions grouped by type
   */
  CoreEventQueue.prototype.availableExtensions = function (settings) {
    var availablePlugins = this.plugins.filter(function (plugin) {
      if (plugin.type !== 'destination' && plugin.name !== 'Segment.io') {
        return true
      }

      var settingValue
      if (plugin.alternativeNames) {
        plugin.alternativeNames.forEach(function (altName) {
          if (settings[altName] !== undefined) {
            settingValue = settings[altName]
          }
        })
      }

      var finalValue = settings[plugin.name] !== undefined ? settings[plugin.name] : settingValue
      return finalValue !== undefined
        ? finalValue
        : plugin.name === 'Segment.io' || settings.All !== false
    })

    var grouped = groupBy()(availablePlugins, 'type')

    return {
      before: grouped.before || [],
      enrichment: grouped.enrichment || [],
      destination: grouped.destination || [],
      after: grouped.after || []
    }
  }

  /**
   * Processes a single context through the plugin pipeline
   * @param {Object} context - Event context
   * @returns {Promise} Processing promise
   */
  CoreEventQueue.prototype.flushOne = function (context) {
    var self = this
    var extensions = this.availableExtensions(context.event.integrations || {})

    // Process before plugins
    var pipeline = Promise.resolve(context)

    if (extensions.before) {
      extensions.before.forEach(function (plugin) {
        pipeline = pipeline.then(function (ctx) {
          return ensurePluginExecution()(ctx, plugin)
        })
      })
    }

    // Process enrichment plugins
    if (extensions.enrichment) {
      extensions.enrichment.forEach(function (plugin) {
        pipeline = pipeline.then(function (ctx) {
          return ensurePluginExecution()(ctx, plugin)
        })
      })
    }

    // Process destination plugins
    if (extensions.destination) {
      extensions.destination.forEach(function (plugin) {
        pipeline = pipeline.then(function (ctx) {
          return ensurePluginExecution()(ctx, plugin)
        })
      })
    }

    // Process after plugins
    if (extensions.after) {
      extensions.after.forEach(function (plugin) {
        pipeline = pipeline.then(function (ctx) {
          return ensurePluginExecution()(ctx, plugin)
        })
      })
    }

    return pipeline
  }

  module.exports = CoreEventQueue
})

console.log('Deobfuscated VS Code Extension - Core utilities loaded')
